import pytest
from appium import webdriver
from appium.options.android import UiAutomator2Options
from appium.webdriver.common.appiumby import AppiumBy
import os


def pytest_addoption(parser):
    """Добавляем кастомные опции командной строки для pytest"""
    parser.addoption(
        "--device_name",
        action="store",
        default="Android Emulator",
        help="Device name for Appium",
    )
    parser.addoption(
        "--platform_version",
        action="store",
        default="13.0",
        help="Android platform version",
    )
    parser.addoption(
        "--app_path",
        action="store",
        default="./test_data/voka.apk",
        help="Path to APK file",
    )


@pytest.fixture(scope="session")
def appium_options(request):
    """Фикстура для настройки опций Appium"""
    options = UiAutomator2Options()

    # Базовые capabilities
    options.set_capability("platformName", "Android")
    options.set_capability("deviceName", request.config.getoption("--device_name"))
    options.set_capability(
        "platformVersion", request.config.getoption("--platform_version")
    )
    options.set_capability("automationName", "UiAutomator2")
    options.set_capability("newCommandTimeout", 300)
    options.set_capability("isHeadless", True)

    # Настройки приложения
    app_path = request.config.getoption("--app_path")
    if os.path.exists(app_path):
        options.set_capability("app", os.path.abspath(app_path))
    else:
        options.set_capability("appPackage", "com.example.app")
        options.set_capability("appActivity", "com.example.app.MainActivity")

    # Дополнительные настройки
    options.set_capability("autoGrantPermissions", True)
    options.set_capability("unicodeKeyboard", True)
    options.set_capability("resetKeyboard", True)

    return options


@pytest.fixture(scope="function")
def driver(appium_options):
    """Основная фикстура для инициализации драйвера Appium"""
    # URL сервера Appium
    appium_server_url = "http://127.0.0.1:4723"

    # Инициализация драйвера
    driver = webdriver.Remote(appium_server_url, options=appium_options)

    # Неявное ожидание
    driver.implicitly_wait(10)

    yield driver

    # Закрытие драйвера после теста
    driver.quit()


# Дополнительные фикстуры для страниц
@pytest.fixture
def base_page(driver):
    from pages.base_page import BasePage

    return BasePage(driver)


@pytest.fixture
def profile_page(driver):
    from pages.profile_page import ProfilePage

    return ProfilePage(driver)
