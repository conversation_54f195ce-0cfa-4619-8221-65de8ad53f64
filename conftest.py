import pytest
from appium import webdriver
from appium.options.android import UiAutomator2Options
import os


# Фикстура для сервера Appium
@pytest.fixture(scope="session")
def appium_server():
    return "http://localhost:4723"


# Фикстура для предустановленного приложения
@pytest.fixture(scope="function")
def android_tv_preinstalled(appium_server):
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "Android TV"
    options.automation_name = "UiAutomator2"
    options.auto_grant_permissions = True

    # Ключевые параметры из вашего лога
    options.app_package = "com.spbtv.velcom"
    options.app_activity = "by.a1.androidTv.screens.main.MainActivity"
    options.no_reset = True

    driver = webdriver.Remote(appium_server, options=options)
    driver.implicitly_wait(10)

    yield driver

    driver.terminate_app(options.app_package)
    driver.quit()


# Фикстура для установки из APK
@pytest.fixture(scope="function")
def android_tv_from_apk(appium_server):
    options = UiAutomator2Options()
    options.platform_name = "Android"
    options.device_name = "Android TV"
    options.automation_name = "UiAutomator2"
    options.auto_grant_permissions = True

    # Установка APK
    apk_path = os.path.join("test_data", "voka.apk")
    absolute_apk_path = os.path.abspath(apk_path)

    # Проверка существования файла
    if not os.path.exists(absolute_apk_path):
        pytest.fail(f"APK файл не найден: {absolute_apk_path}")

    options.app = absolute_apk_path
    options.full_reset = True

    driver = webdriver.Remote(appium_server, options=options)
    driver.implicitly_wait(10)

    yield driver

    driver.remove_app("com.spbtv.velcom")
    driver.quit()


# Фикстура для страницы профиля (добавьте эту фикстуру)
@pytest.fixture(scope="function")
def profile_page(android_tv_preinstalled):
    # Здесь должна быть инициализация вашего Page Object
    # Например: return ProfilePage(android_tv_preinstalled)
    # Если у вас есть класс ProfilePage, раскомментируйте и импортируйте его
    pass
