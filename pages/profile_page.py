from appium.webdriver.common.appiumby import AppiumBy
from pages.base_page import BasePage
from test_data import creds

formatted_ott_login = creds.formated_ott_login(creds.ott_login)


class ProfilePageLocators:
    check_iptv_login = (
        AppiumBy.XPATH,
        f'//android.widget.TextView[@text="{creds.iptv_login}"]',
    )

    check_ott_login = (
        AppiumBy.XPATH,
        f'//android.widget.TextView[@text="{formatted_ott_login}"]',
    )


class ProfilePage(BasePage):
    def __init__(self, driver):
        self.driver = driver

    def close_onboarding(self):
        self.move_up()
        self.select()

    def open_profile(self):
        self.move_down(5)
        self.select()

    def get_iptv_login_text(self):
        return self.get_text_from_element(ProfilePageLocators.check_iptv_login)
