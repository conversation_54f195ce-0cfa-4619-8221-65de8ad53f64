2025-09-09 15:08:32,559 - conftest - INFO - Подключение к Appium серверу: http://127.0.0.1:4723
2025-09-09 15:08:48,008 - conftest - INFO - Подключение к Appium серверу: http://127.0.0.1:4723
2025-09-09 15:09:04,298 - conftest - INFO - Драйвер успешно инициализирован
2025-09-09 15:09:04,298 - tests.test_iptv_auth - INFO - Начало теста автоматической авторизации
2025-09-09 15:09:04,298 - tests.test_iptv_auth - INFO - Закрытие экрана онбординга
2025-09-09 15:09:04,592 - tests.test_iptv_auth - INFO - Открытие страницы профиля
2025-09-09 15:09:05,482 - tests.test_iptv_auth - INFO - Проверка IPTV логина
2025-09-09 15:09:19,098 - pages.base_page - ERROR - Элемент не найден в течение 10 секунд: ('xpath', '//android.widget.TextView[@text="000206264408"]')
2025-09-09 15:09:19,115 - conftest - INFO - Закрытие драйвера
2025-09-09 15:10:19,527 - conftest - INFO - Подключение к Appium серверу: http://127.0.0.1:4723
2025-09-09 15:10:19,595 - conftest - ERROR - Ошибка при инициализации драйвера: Message: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
Stacktrace:
UnknownError: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at getResponseForW3CError (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.ts:1103:34)
    at asyncHandler (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.ts:507:57)
The above error is caused by
Error: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at Object.errorWithException (/usr/lib/node_modules/appium/node_modules/@appium/support/lib/logging.js:41:45)
    at AndroidUiautomator2Driver.getDeviceInfoFromCaps (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/device/common.js:115:24)
    at AndroidUiautomator2Driver.createSession (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/driver.ts:357:30)
    at AppiumDriver.createSession (/usr/lib/node_modules/appium/lib/appium.js:360:35)
2025-09-09 15:10:43,820 - conftest - INFO - Подключение к Appium серверу: http://127.0.0.1:4723
2025-09-09 15:10:43,891 - conftest - ERROR - Ошибка при инициализации драйвера: Message: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
Stacktrace:
UnknownError: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at getResponseForW3CError (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.ts:1103:34)
    at asyncHandler (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.ts:507:57)
The above error is caused by
Error: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at Object.errorWithException (/usr/lib/node_modules/appium/node_modules/@appium/support/lib/logging.js:41:45)
    at AndroidUiautomator2Driver.getDeviceInfoFromCaps (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/device/common.js:115:24)
    at AndroidUiautomator2Driver.createSession (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/driver.ts:357:30)
    at AppiumDriver.createSession (/usr/lib/node_modules/appium/lib/appium.js:360:35)
2025-09-09 15:11:04,546 - conftest - INFO - Подключение к Appium серверу: http://127.0.0.1:4723
2025-09-09 15:11:04,617 - conftest - ERROR - Ошибка при инициализации драйвера: Message: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
Stacktrace:
UnknownError: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at getResponseForW3CError (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.ts:1103:34)
    at asyncHandler (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.ts:507:57)
The above error is caused by
Error: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at Object.errorWithException (/usr/lib/node_modules/appium/node_modules/@appium/support/lib/logging.js:41:45)
    at AndroidUiautomator2Driver.getDeviceInfoFromCaps (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/device/common.js:115:24)
    at AndroidUiautomator2Driver.createSession (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/driver.ts:357:30)
    at AppiumDriver.createSession (/usr/lib/node_modules/appium/lib/appium.js:360:35)
2025-09-09 15:11:17,071 - conftest - INFO - Подключение к Appium серверу: http://127.0.0.1:4723
2025-09-09 15:11:17,135 - conftest - ERROR - Ошибка при инициализации драйвера: Message: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
Stacktrace:
UnknownError: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at getResponseForW3CError (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.ts:1103:34)
    at asyncHandler (/usr/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.ts:507:57)
The above error is caused by
Error: Unable to find an active device or emulator with OS 13.0. The following are available: *************:5555 (9)
    at Object.errorWithException (/usr/lib/node_modules/appium/node_modules/@appium/support/lib/logging.js:41:45)
    at AndroidUiautomator2Driver.getDeviceInfoFromCaps (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/node_modules/appium-android-driver/lib/commands/device/common.js:115:24)
    at AndroidUiautomator2Driver.createSession (/home/<USER>/.appium/node_modules/appium-uiautomator2-driver/lib/driver.ts:357:30)
    at AppiumDriver.createSession (/usr/lib/node_modules/appium/lib/appium.js:360:35)
